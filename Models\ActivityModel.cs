using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace UFU2.Models
{
    /// <summary>
    /// Model class representing activity information for client registration.
    /// Implements INotifyPropertyChanged for proper WPF data binding support.
    /// Supports all activity types: Main Commercial, Secondary Commercial, Craft, and Professional.
    /// 
    /// This model follows UFU2 architectural patterns with:
    /// - Property change notification for real-time UI updates
    /// - Comprehensive validation support (future implementation)
    /// - Arabic RTL layout compatibility
    /// - MaterialDesign form field integration
    /// </summary>
    public class ActivityModel : INotifyPropertyChanged
    {
        #region Private Fields

        private string _activityCode = string.Empty;
        private string _activityDescription = string.Empty;
        private string _activityStatus = string.Empty;
        private string _activityStartDate = string.Empty;
        private string _commercialRegister = string.Empty;
        private string _activityLocation = string.Empty;
        private string _nifNumber = string.Empty;
        private string _nisNumber = string.Empty;
        private string _artNumber = string.Empty;
        private string _cpiDaira = string.Empty;
        private string _cpiWilaya = string.Empty;
        private string _activityUpdateDate = string.Empty;
        private string _activityUpdateNote = string.Empty;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the official activity code.
        /// Used for government registration and identification purposes.
        /// </summary>
        public string ActivityCode
        {
            get => _activityCode;
            set => SetProperty(ref _activityCode, value);
        }

        /// <summary>
        /// Gets or sets the activity description or name.
        /// Describes the nature of the business activity.
        /// Used for Craft and Professional activity types.
        /// </summary>
        public string ActivityDescription
        {
            get => _activityDescription;
            set => SetProperty(ref _activityDescription, value);
        }

        /// <summary>
        /// Gets or sets the current status of the activity.
        /// Values depend on activity type (Active, Inactive, Pending, etc.).
        /// </summary>
        public string ActivityStatus
        {
            get => _activityStatus;
            set => SetProperty(ref _activityStatus, value);
        }

        /// <summary>
        /// Gets or sets the activity start date in DD/MM/YYYY format.
        /// Supports special "x" placeholder dates (xx/xx/YYYY) for unknown values.
        /// </summary>
        public string ActivityStartDate
        {
            get => _activityStartDate;
            set => SetProperty(ref _activityStartDate, value);
        }

        /// <summary>
        /// Gets or sets the commercial register number.
        /// Required for commercial activities, optional for others.
        /// </summary>
        public string CommercialRegister
        {
            get => _commercialRegister;
            set => SetProperty(ref _commercialRegister, value);
        }

        /// <summary>
        /// Gets or sets the physical location where the activity is conducted.
        /// Full address including street, city, and region information.
        /// </summary>
        public string ActivityLocation
        {
            get => _activityLocation;
            set => SetProperty(ref _activityLocation, value);
        }

        /// <summary>
        /// Gets or sets the NIF (Numéro d'Identification Fiscale) number.
        /// Tax identification number for fiscal purposes.
        /// </summary>
        public string NifNumber
        {
            get => _nifNumber;
            set => SetProperty(ref _nifNumber, value);
        }

        /// <summary>
        /// Gets or sets the NIS (Numéro d'Identification Statistique) number.
        /// Statistical identification number for government reporting.
        /// </summary>
        public string NisNumber
        {
            get => _nisNumber;
            set => SetProperty(ref _nisNumber, value);
        }

        /// <summary>
        /// Gets or sets the ART (Article) number.
        /// Article number for specific activity classification.
        /// </summary>
        public string ArtNumber
        {
            get => _artNumber;
            set => SetProperty(ref _artNumber, value);
        }

        /// <summary>
        /// Gets or sets the CPI Draira (Municipal Tax Office) information.
        /// Municipal tax office responsible for local taxation.
        /// </summary>
        public string CpiDaira
        {
            get => _cpiDaira;
            set => SetProperty(ref _cpiDaira, value);
        }

        /// <summary>
        /// Gets or sets the CPI Wilaya (Provincial Tax Office) information.
        /// Provincial tax office responsible for regional taxation.
        /// </summary>
        public string CpiWilaya
        {
            get => _cpiWilaya;
            set => SetProperty(ref _cpiWilaya, value);
        }

        /// <summary>
        /// Gets or sets the activity update date in DD/MM/YYYY format.
        /// Used to track when activity status was last modified.
        /// Supports placeholder format "xx/xx/xxxx" for unknown dates.
        /// </summary>
        public string ActivityUpdateDate
        {
            get => _activityUpdateDate;
            set => SetProperty(ref _activityUpdateDate, value);
        }

        /// <summary>
        /// Gets or sets the activity update note.
        /// Contains additional information about status changes or modifications.
        /// Supports Arabic RTL text input.
        /// </summary>
        public string ActivityUpdateNote
        {
            get => _activityUpdateNote;
            set => SetProperty(ref _activityUpdateNote, value);
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property value and raises PropertyChanged event if the value has changed.
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value to set</param>
        /// <param name="propertyName">The name of the property (automatically provided)</param>
        /// <returns>True if the property was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Resets all activity properties to their default empty values.
        /// Useful when switching between activity types or clearing the form.
        /// </summary>
        public void Reset()
        {
            ActivityCode = string.Empty;
            ActivityDescription = string.Empty;
            ActivityStatus = string.Empty;
            ActivityStartDate = string.Empty;
            CommercialRegister = string.Empty;
            ActivityLocation = string.Empty;
            NifNumber = string.Empty;
            NisNumber = string.Empty;
            ArtNumber = string.Empty;
            CpiDaira = string.Empty;
            CpiWilaya = string.Empty;
            ActivityUpdateDate = string.Empty;
            ActivityUpdateNote = string.Empty;
        }

        /// <summary>
        /// Validates the activity model based on the specified activity type.
        /// Returns true if all required fields for the activity type are filled.
        /// </summary>
        /// <param name="activityType">The type of activity to validate against</param>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid(string activityType)
        {
            // Basic validation - all activity types require description and location
            if (string.IsNullOrWhiteSpace(ActivityDescription) || 
                string.IsNullOrWhiteSpace(ActivityLocation))
                return false;

            // Activity type specific validation
            switch (activityType)
            {
                case "MainCommercial":
                case "SecondaryCommercial":
                    // Commercial activities require commercial register
                    return !string.IsNullOrWhiteSpace(CommercialRegister);
                
                case "Craft":
                case "Professional":
                    // Craft and professional activities require activity code
                    return !string.IsNullOrWhiteSpace(ActivityCode);
                
                default:
                    return true;
            }
        }

        /// <summary>
        /// Creates a copy of the current ActivityModel instance.
        /// </summary>
        /// <returns>A new ActivityModel with the same property values</returns>
        public ActivityModel Clone()
        {
            return new ActivityModel
            {
                ActivityCode = this.ActivityCode,
                ActivityDescription = this.ActivityDescription,
                ActivityStatus = this.ActivityStatus,
                ActivityStartDate = this.ActivityStartDate,
                CommercialRegister = this.CommercialRegister,
                ActivityLocation = this.ActivityLocation,
                NifNumber = this.NifNumber,
                NisNumber = this.NisNumber,
                ArtNumber = this.ArtNumber,
                CpiDaira = this.CpiDaira,
                CpiWilaya = this.CpiWilaya,
                ActivityUpdateDate = this.ActivityUpdateDate,
                ActivityUpdateNote = this.ActivityUpdateNote
            };
        }

        #endregion
    }
}
