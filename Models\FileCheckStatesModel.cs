using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace UFU2.Models
{
    /// <summary>
    /// Model class representing file check states for client registration.
    /// Implements INotifyPropertyChanged for proper WPF data binding support.
    /// Tracks the completion status of various required documents based on activity type.
    /// 
    /// This model follows UFU2 architectural patterns with:
    /// - Property change notification for real-time UI updates
    /// - Comprehensive file check tracking for all activity types
    /// - Arabic RTL layout compatibility
    /// - MaterialDesign checkbox integration
    /// </summary>
    public class FileCheckStatesModel : INotifyPropertyChanged
    {
        #region Private Fields

        private bool _casChipChecked;
        private bool _nifChipChecked;
        private bool _nisChipChecked;
        private bool _rcChipChecked;
        private bool _artChipChecked;
        private bool _agrChipChecked;
        private bool _dexChipChecked;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets whether the CAS (Social Security) file check is completed.
        /// Required for all activity types.
        /// </summary>
        public bool CasChipChecked
        {
            get => _casChipChecked;
            set => SetProperty(ref _casChipChecked, value);
        }

        /// <summary>
        /// Gets or sets whether the NIF (Tax ID) file check is completed.
        /// Required for all activity types.
        /// </summary>
        public bool NifChipChecked
        {
            get => _nifChipChecked;
            set => SetProperty(ref _nifChipChecked, value);
        }

        /// <summary>
        /// Gets or sets whether the NIS (Statistical ID) file check is completed.
        /// Required for all activity types.
        /// </summary>
        public bool NisChipChecked
        {
            get => _nisChipChecked;
            set => SetProperty(ref _nisChipChecked, value);
        }

        /// <summary>
        /// Gets or sets whether the RC (Commercial Register) file check is completed.
        /// Required only for MainCommercial and SecondaryCommercial activity types.
        /// </summary>
        public bool RcChipChecked
        {
            get => _rcChipChecked;
            set => SetProperty(ref _rcChipChecked, value);
        }

        /// <summary>
        /// Gets or sets whether the ART (Craft Card) file check is completed.
        /// Required only for Craft activity type.
        /// </summary>
        public bool ArtChipChecked
        {
            get => _artChipChecked;
            set => SetProperty(ref _artChipChecked, value);
        }

        /// <summary>
        /// Gets or sets whether the AGR (Professional Certificate) file check is completed.
        /// Required only for Professional activity type.
        /// </summary>
        public bool AgrChipChecked
        {
            get => _agrChipChecked;
            set => SetProperty(ref _agrChipChecked, value);
        }

        /// <summary>
        /// Gets or sets whether the DEx (Existence Declaration) file check is completed.
        /// Required for all activity types.
        /// </summary>
        public bool DexChipChecked
        {
            get => _dexChipChecked;
            set => SetProperty(ref _dexChipChecked, value);
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Sets the property value and raises PropertyChanged event if the value has changed.
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">The new value to set</param>
        /// <param name="propertyName">The name of the property (automatically provided by CallerMemberName)</param>
        /// <returns>True if the property value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Resets all file check states to unchecked.
        /// Useful when switching between activity types or clearing the form.
        /// </summary>
        public void Reset()
        {
            CasChipChecked = false;
            NifChipChecked = false;
            NisChipChecked = false;
            RcChipChecked = false;
            ArtChipChecked = false;
            AgrChipChecked = false;
            DexChipChecked = false;
        }

        /// <summary>
        /// Validates the file check states based on the specified activity type.
        /// Uses FileCheckTypeRules for centralized business rule validation.
        /// Returns true if all required file checks for the activity type are completed.
        /// </summary>
        /// <param name="activityType">The type of activity to validate against</param>
        /// <returns>True if all required checks are completed, false otherwise</returns>
        public bool IsValid(string activityType)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(activityType))
                    return false;

                // Get required file check types from business rules
                var requiredTypes = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);
                
                // Check each required type
                foreach (var requiredType in requiredTypes)
                {
                    var isChecked = requiredType.ToUpperInvariant() switch
                    {
                        "CAS" => CasChipChecked,
                        "NIF" => NifChipChecked,
                        "NIS" => NisChipChecked,
                        "RC" => RcChipChecked,
                        "ART" => ArtChipChecked,
                        "AGR" => AgrChipChecked,
                        "DEX" => DexChipChecked,
                        _ => false
                    };

                    if (!isChecked)
                        return false;
                }

                return true;
            }
            catch (Exception)
            {
                // Fallback to legacy validation logic
                if (!CasChipChecked || !NifChipChecked || !NisChipChecked || !DexChipChecked)
                    return false;

                return activityType switch
                {
                    "MainCommercial" => RcChipChecked,
                    "SecondaryCommercial" => RcChipChecked,
                    "Craft" => ArtChipChecked,
                    "Professional" => AgrChipChecked,
                    _ => true
                };
            }
        }

        /// <summary>
        /// Gets the completion percentage for the current activity type.
        /// </summary>
        /// <param name="activityType">The type of activity to calculate completion for</param>
        /// <returns>Completion percentage (0.0 to 1.0)</returns>
        public double GetCompletionPercentage(string activityType)
        {
            var requiredChecks = GetRequiredCheckCount(activityType);
            var completedChecks = GetCompletedCheckCount(activityType);
            
            return requiredChecks > 0 ? (double)completedChecks / requiredChecks : 0.0;
        }

        /// <summary>
        /// Gets the number of required file checks for the specified activity type.
        /// </summary>
        /// <param name="activityType">The type of activity</param>
        /// <returns>Number of required file checks</returns>
        private int GetRequiredCheckCount(string activityType)
        {
            return activityType switch
            {
                "MainCommercial" => 5, // CAS, NIF, NIS, RC, DEx
                "SecondaryCommercial" => 5, // CAS, NIF, NIS, RC, DEx
                "Craft" => 5, // CAS, NIF, NIS, ART, DEx
                "Professional" => 5, // CAS, NIF, NIS, AGR, DEx
                _ => 4 // Default: CAS, NIF, NIS, DEx
            };
        }

        /// <summary>
        /// Gets the number of completed file checks for the specified activity type.
        /// </summary>
        /// <param name="activityType">The type of activity</param>
        /// <returns>Number of completed file checks</returns>
        private int GetCompletedCheckCount(string activityType)
        {
            var count = 0;
            
            // Count basic required checks
            if (CasChipChecked) count++;
            if (NifChipChecked) count++;
            if (NisChipChecked) count++;
            if (DexChipChecked) count++;
            
            // Count activity-specific checks
            switch (activityType)
            {
                case "MainCommercial":
                case "SecondaryCommercial":
                    if (RcChipChecked) count++;
                    break;
                case "Craft":
                    if (ArtChipChecked) count++;
                    break;
                case "Professional":
                    if (AgrChipChecked) count++;
                    break;
            }
            
            return count;
        }

        /// <summary>
        /// Gets validation errors for incomplete required file checks.
        /// </summary>
        /// <param name="activityType">The activity type to validate against</param>
        /// <returns>List of Arabic error messages for incomplete file checks</returns>
        public List<string> GetValidationErrors(string activityType)
        {
            var errors = new List<string>();

            try
            {
                if (string.IsNullOrWhiteSpace(activityType))
                {
                    errors.Add("نوع النشاط مطلوب للتحقق من حالة فحص الملفات");
                    return errors;
                }

                var requiredTypes = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);
                var activityDisplayName = FileCheckTypeRules.GetActivityTypeDisplayName(activityType);

                foreach (var requiredType in requiredTypes)
                {
                    var isChecked = requiredType.ToUpperInvariant() switch
                    {
                        "CAS" => CasChipChecked,
                        "NIF" => NifChipChecked,
                        "NIS" => NisChipChecked,
                        "RC" => RcChipChecked,
                        "ART" => ArtChipChecked,
                        "AGR" => AgrChipChecked,
                        "DEX" => DexChipChecked,
                        _ => false
                    };

                    if (!isChecked)
                    {
                        var fileCheckDisplayName = FileCheckTypeRules.GetFileCheckTypeDisplayName(requiredType);
                        errors.Add($"فحص الملف '{fileCheckDisplayName}' مطلوب لنوع النشاط '{activityDisplayName}'");
                    }
                }
            }
            catch (Exception)
            {
                errors.Add("حدث خطأ أثناء التحقق من حالة فحص الملفات");
            }

            return errors;
        }

        /// <summary>
        /// Creates a copy of the current FileCheckStatesModel instance.
        /// </summary>
        /// <returns>A new FileCheckStatesModel with the same property values</returns>
        public FileCheckStatesModel Clone()
        {
            return new FileCheckStatesModel
            {
                CasChipChecked = this.CasChipChecked,
                NifChipChecked = this.NifChipChecked,
                NisChipChecked = this.NisChipChecked,
                RcChipChecked = this.RcChipChecked,
                ArtChipChecked = this.ArtChipChecked,
                AgrChipChecked = this.AgrChipChecked,
                DexChipChecked = this.DexChipChecked
            };
        }

        #endregion
    }
}
