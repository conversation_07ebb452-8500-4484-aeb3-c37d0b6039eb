{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving code quality while maintaining existing functionality with focus on readability, maintainability, and performance optimizations.", "version": "1", "when": {"type": "userTriggered", "patterns": ["*.cs", "*.xaml", "*.xaml.cs"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Quality Analysis**:\n   - Identify code smells and anti-patterns\n   - Check for SOLID principle violations\n   - Review naming conventions and consistency\n   - Assess method complexity and length\n\n2. **WPF/MVVM Best Practices**:\n   - Verify proper MVVM pattern implementation\n   - Check data binding practices\n   - Review command usage and implementation\n   - Assess view-viewmodel separation\n\n3. **Performance Optimizations**:\n   - Identify potential memory leaks\n   - Review database query efficiency\n   - Check for unnecessary object allocations\n   - Assess UI thread blocking operations\n\n4. **Maintainability Improvements**:\n   - Suggest better error handling patterns\n   - Recommend code documentation improvements\n   - Identify opportunities for code reuse\n   - Propose refactoring for better readability\n\n5. **UFU2-Specific Considerations**:\n   - Ensure consistency with existing architecture patterns\n   - Verify proper use of ServiceLocator and dependency injection\n   - Check Material Design theme compliance\n   - Review database service usage patterns\n\n**Guidelines**:\n- Implement the simplest solution that meets requirements\n- Leverage existing patterns and code before creating new solutions\n- Provide comprehensive documentation for all suggested changes\n- Analyze root causes thoroughly before proposing fixes\n- Provide minimal, precise solutions\n- Document issue and resolution clearly\n\nProvide specific, actionable recommendations with code examples where appropriate. Maintain the existing functionality while improving code quality."}}