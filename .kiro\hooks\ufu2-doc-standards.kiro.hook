{"enabled": true, "name": "UFU2 Documentation Standards", "description": "Monitors C# files in key UFU2 folders and prompts for documentation creation when components are added or modified, ensuring all components have proper Markdown documentation with required sections and Mermaid diagrams", "version": "1", "when": {"type": "fileEdited", "patterns": ["Commands/*.cs", "Common/*.cs", "Services/*.cs", "ViewModels/*.cs", "Models/*.cs", "Helpers/*.cs", "Converters/*.cs"]}, "then": {"type": "askAgent", "prompt": "A UFU2 component has been modified. Please review the changed file and ensure it has proper documentation in the Documentation/ folder following UFU2 Documentation Standards:\n\n1. Create/update Markdown file in Documentation/ folder for this component\n2. Include mandatory sections: Overview, What the Code Does, How It's Used (with code examples), Integration with UFU2 Architecture, Performance Considerations, Mermaid Diagram\n3. Add at least one Mermaid diagram showing component relationships, data flow, or state transitions\n4. Provide real working code examples for all public methods\n5. Include both basic and advanced usage patterns with error handling\n\nFocus on the specific component that was changed and ensure it integrates properly with the UFU2 WPF MVVM architecture."}}