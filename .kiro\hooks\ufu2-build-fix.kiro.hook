{"enabled": true, "name": "UFU2 Build & Fix", "description": "Monitors UFU2 project files for changes and automatically builds the project, analyzing and fixing any compilation errors that occur. Maintains MVVM architecture, service patterns, and MaterialDesign styling while ensuring Arabic RTL compliance.", "version": "1", "when": {"type": "userTriggered", "patterns": ["tasks.md"]}, "then": {"type": "askAgent", "prompt": "Build the UFU2 WPF application project and resolve any compilation errors that occur. Please:  1. Execute the build process using the appropriate .NET build command (likely `dotnet build` or MSBuild) 2. Analyze any compilation errors, warnings, or build failures that are reported 3. For each error found:    - Identify the root cause (missing references, syntax errors, type mismatches, etc.)    - Implement the necessary fixes following UFU2's established patterns:      - Maintain MVVM architecture with BaseViewModel inheritance      - Use existing service patterns (ServiceLocator, ErrorManager, LoggingService)      - Follow MaterialDesign styling conventions      - Ensure Arabic RTL support compliance 4. Re-run the build after each fix to verify the resolution 5. Continue this process until the project builds successfully without errors 6. Provide a summary of what errors were found and how they were resolved  Focus only on compilation errors that prevent the build from succeeding - do not address warnings unless they are blocking the build process."}}