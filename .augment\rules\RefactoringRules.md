---
type: "manual"
---

# UFU2 Safe Refactoring Rules

This document outlines mandatory rules and best practices to follow when refactoring existing code. The primary goal is to **prevent breaking existing functionality** and **avoid losing code**.

## Core Principles

1.  **Preserve Behavior:** The refactored code must behave *identically* to the original code under all expected conditions.
2.  **Incremental Changes:** Prefer small, focused refactoring steps that can be easily tested and verified over large, sweeping changes.
3.  **Maintain Dependencies:** Ensure all necessary dependencies (services, events, properties) are correctly transferred or made accessible to the new components.
4.  **No Logic Changes During Extraction:** The extraction process itself should *not* involve changing the underlying logic, algorithms, or business rules. Focus solely on moving code.

## Pre-Refactoring Checklist

Before starting the refactoring process:

1.  **Ensure Test Coverage:** Verify that adequate unit or integration tests exist for the section of code you plan to refactor. If tests are lacking, write them *first* based on the current behavior.
2.  **Backup Original File:** As per `RefactoringChecklist.md`, create a `.backup` of the original file.
3.  **Identify Extraction Boundaries:** Clearly define the start and end points of the code block to be extracted. Ensure it represents a coherent piece of functionality.

## Safe Extraction Process

Follow these steps when extracting code:

1.  **Copy, Don't Cut (Initially):**
    *   *Do not* immediately cut/delete the code from the original class.
    *   Copy the selected block of code (methods, properties, fields if necessary) into the new class file.
2.  **Resolve New Class Dependencies:**
    *   Add necessary `using` directives to the new file.
    *   Identify dependencies the copied code has on the original class (e.g., private fields, other methods, services accessed via the original class).
    *   Provide these dependencies to the new class. Common approaches:
        *   Pass required data/objects as parameters to the new class's constructor or methods.
        *   Inject necessary services via the constructor (using `ServiceLocator` if appropriate, or constructor injection if the new class is registered in the DI container).
        *   If the new class needs to communicate back to the original ViewModel (e.g., to trigger a property change or update state), use events or callbacks passed in via the constructor.
3.  **Update Original Class to Use New Class:**
    *   In the original class, instantiate the new class (preferably via `ServiceLocator` if it's a significant service-like component, or directly if it's a helper/extracted ViewModel part).
    *   Pass the necessary dependencies (services, data, callbacks) to the new class instance.
    *   Modify the original methods to delegate the work to the new class instance.
    *   Ensure the original public API (properties, commands) remains unchanged or is updated to correctly interact with the new component.
4.  **Test Incrementally:**
    *   After wiring up the new class, run relevant tests (unit, integration, or manual UI tests) to verify that the behavior is unchanged.
5.  **Remove Original Code:**
    *   *Only after* verifying the new implementation works correctly, remove the original copied code block from the main class.
6.  **Refine and Optimize (Optional):**
    *   Once the extraction is complete and verified, you *may* then proceed to optimize or improve the logic within the *new* class, but this should be a separate step from the initial extraction.

## Best Practices for Extracted Components

*   **Single Responsibility:** The new class should have a single, well-defined responsibility related to the extracted functionality.
*   **Clear Interface:** Define clear public methods and properties for the new class. Minimize direct access to its internal state from the original class.
*   **Dependency Management:** Prefer dependency injection (constructor injection or `ServiceLocator`) for services over accessing them through static properties or the original class.
*   **Naming:** Give the new class a descriptive name that reflects its specific responsibility.
*   **Inheritance:** If extracting part of a ViewModel, the new class *might* inherit from `BaseViewModel` if it needs its features, or it could be a simple service/helper class if it doesn't manage its own UI state directly.

## Anti-Patterns to Avoid

*   ❌ **Cutting Code First:** Deleting or cutting code from the original class before the new implementation is fully integrated and tested.
*   ❌ **Changing Logic During Extraction:** Modifying business rules, algorithms, or complex conditional logic *while* extracting. Do this in a subsequent, separate refactoring step.
*   ❌ **Tight Coupling:** Creating a new class that is tightly coupled to the internals of the original class. Use defined interfaces, parameters, and events.
*   ❌ **Ignoring Dependencies:** Forgetting to pass necessary services or data to the new class, leading to `NullReferenceExceptions` or incorrect behavior.
*   ❌ **Skipping Tests:** Refactoring significant logic without verifying behavior through tests.

By strictly adhering to these rules, you can confidently refactor large classes by extracting functionality into smaller, more manageable components without risking the stability or correctness of the application.