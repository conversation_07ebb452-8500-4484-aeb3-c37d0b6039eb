using System;
using System.ComponentModel;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the ActivityStatusUpdateDialog providing MVVM data binding and command handling.
    /// Manages activity update date and note editing with validation.
    /// Implements IDataErrorInfo for real-time validation feedback with Arabic error messages.
    ///
    /// REFACTORING STATUS: ✅ COMPLETED - ValidationService integration (Task 3.3)
    /// BACKUP CREATED: ActivityStatusUpdateDialogViewModel.cs.backup2 - Implementation before ValidationService integration
    /// PREVIOUS BACKUP: ActivityStatusUpdateDialogViewModel.cs.backup - Original implementation before IDisposable enhancement
    /// </summary>
    public class ActivityStatusUpdateDialogViewModel : BaseViewModel, IDataErrorInfo
    {
        #region Private Fields

        private string _updateDate = string.Empty;
        private string _updateNote = string.Empty;
        private string _activityStatus = string.Empty;
        private string _activityType = string.Empty;
        private readonly ValidationService _validationService;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the activity update date in DD/MM/YYYY format.
        /// Supports placeholder format "xx/xx/xxxx" for unknown dates.
        /// </summary>
        public string UpdateDate
        {
            get => _updateDate;
            set => SetProperty(ref _updateDate, value);
        }

        /// <summary>
        /// Gets or sets the activity update note.
        /// Contains additional information about status changes or modifications.
        /// </summary>
        public string UpdateNote
        {
            get => _updateNote;
            set => SetProperty(ref _updateNote, value);
        }

        /// <summary>
        /// Gets or sets the current activity status that triggered this dialog.
        /// Used for validation and display purposes.
        /// </summary>
        public string ActivityStatus
        {
            get => _activityStatus;
            set => SetProperty(ref _activityStatus, value);
        }

        /// <summary>
        /// Gets or sets the current activity type.
        /// Used for context-specific validation and display.
        /// </summary>
        public string ActivityType
        {
            get => _activityType;
            set => SetProperty(ref _activityType, value);
        }

        /// <summary>
        /// Gets the dialog header text based on the activity status.
        /// </summary>
        public string DialogHeader
        {
            get
            {
                return ActivityStatus switch
                {
                    "معدل" => "تحديث معلومات النشاط المعدل",
                    "غير نشط" => "تحديث معلومات النشاط غير النشط",
                    "شطب" => "تحديث معلومات النشاط المشطوب",
                    _ => "تحديث معلومات النشاط"
                };
            }
        }

        /// <summary>
        /// Gets whether the current input is valid for saving.
        /// </summary>
        public bool IsValid => IsDateValid && IsNoteValid;

        /// <summary>
        /// Gets whether the update date is valid.
        /// </summary>
        public bool IsDateValid => string.IsNullOrEmpty(_validationService?.ValidateDate(UpdateDate, true));

        /// <summary>
        /// Gets whether the update note is valid.
        /// </summary>
        public bool IsNoteValid => string.IsNullOrEmpty(_validationService?.ValidateOptionalNote(UpdateNote, 500));

        #endregion

        #region Commands

        /// <summary>
        /// Command for saving the activity update information.
        /// </summary>
        public ICommand SaveCommand { get; }

        /// <summary>
        /// Command for canceling the dialog.
        /// </summary>
        public ICommand CancelCommand { get; }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when save is requested.
        /// </summary>
        public event Action? SaveRequested;

        /// <summary>
        /// Event raised when cancel is requested.
        /// </summary>
        public event Action? CancelRequested;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the ActivityStatusUpdateDialogViewModel.
        /// </summary>
        public ActivityStatusUpdateDialogViewModel()
        {
            // Initialize ValidationService through ServiceLocator
            _validationService = ServiceLocator.GetService<ValidationService>();

            // Initialize commands
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave, "SaveActivityUpdate");
            CancelCommand = new RelayCommand(ExecuteCancel, CanExecuteCancel, "CancelActivityUpdate");

            // Set default values
            UpdateDate = DateTime.Now.ToString("dd/MM/yyyy");
            UpdateNote = string.Empty;

            // Initialize BaseViewModel
            OnInitialize();

            LoggingService.LogDebug("ActivityStatusUpdateDialogViewModel initialized with ValidationService integration", "ActivityStatusUpdateDialogViewModel");
        }

        /// <summary>
        /// Initializes a new instance with existing data.
        /// </summary>
        /// <param name="existingDate">Existing update date</param>
        /// <param name="existingNote">Existing update note</param>
        /// <param name="activityStatus">Current activity status</param>
        /// <param name="activityType">Current activity type</param>
        public ActivityStatusUpdateDialogViewModel(string existingDate, string existingNote, string activityStatus, string activityType)
            : this()
        {
            UpdateDate = string.IsNullOrWhiteSpace(existingDate) ? DateTime.Now.ToString("dd/MM/yyyy") : existingDate;
            UpdateNote = existingNote ?? string.Empty;
            ActivityStatus = activityStatus ?? string.Empty;
            ActivityType = activityType ?? string.Empty;

            LoggingService.LogDebug($"ActivityStatusUpdateDialogViewModel initialized with existing data - Status: {ActivityStatus}, Type: {ActivityType}", "ActivityStatusUpdateDialogViewModel");
        }

        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets the error message for the object.
        /// </summary>
        public string? Error
        {
            get
            {
                if (!IsDateValid) return "تاريخ التحديث غير صحيح";
                if (!IsNoteValid) return "ملاحظة التحديث غير صحيحة";
                return null;
            }
        }

        /// <summary>
        /// Gets the error message for the specified property.
        /// </summary>
        /// <param name="columnName">The property name</param>
        /// <returns>Error message or null if valid</returns>
        public string? this[string columnName]
        {
            get
            {
                return columnName switch
                {
                    nameof(UpdateDate) => _validationService?.ValidateDate(UpdateDate, true),
                    nameof(UpdateNote) => _validationService?.ValidateOptionalNote(UpdateNote, 500),
                    _ => null
                };
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Applies default values to empty fields when saving.
        /// UpdateDate gets current date in dd/MM/yyyy format.
        /// UpdateNote gets Arabic default text "لا توجد معلومات إضافية".
        /// </summary>
        private void ApplyDefaultValuesIfEmpty()
        {
            try
            {
                // Auto-populate UpdateDate with current date if empty
                if (string.IsNullOrWhiteSpace(UpdateDate))
                {
                    UpdateDate = DateTime.Now.ToString("dd/MM/yyyy");
                    LoggingService.LogInfo("Auto-populated empty UpdateDate with current date", "ActivityStatusUpdateDialogViewModel");
                }

                // Auto-populate UpdateNote with Arabic default text if empty
                if (string.IsNullOrWhiteSpace(UpdateNote))
                {
                    UpdateNote = "لا توجد معلومات إضافية";
                    LoggingService.LogInfo("Auto-populated empty UpdateNote with Arabic default text", "ActivityStatusUpdateDialogViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error applying default values: {ex.Message}", "ActivityStatusUpdateDialogViewModel");
                ErrorManager.LogException(ex, LogLevel.Warning, "ActivityStatusUpdateDialogViewModel");
            }
        }



        /// <summary>
        /// Executes the save command.
        /// Automatically populates default values for empty fields before validation.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private void ExecuteSave(object? parameter)
        {
            try
            {
                // Auto-populate default values for empty fields before validation
                ApplyDefaultValuesIfEmpty();

                if (!IsValid)
                {
                    LoggingService.LogWarning("Attempted to save activity update with invalid data", "ActivityStatusUpdateDialogViewModel");
                    return;
                }

                LoggingService.LogInfo($"Saving activity update - Date: {UpdateDate}, Note length: {UpdateNote?.Length ?? 0}", "ActivityStatusUpdateDialogViewModel");
                SaveRequested?.Invoke();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing save command: {ex.Message}", "ActivityStatusUpdateDialogViewModel");
                ErrorManager.LogException(ex, LogLevel.Error, "ActivityStatusUpdateDialogViewModel");
            }
        }

        /// <summary>
        /// Determines whether the save command can be executed.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>True if save can be executed</returns>
        private bool CanExecuteSave(object? parameter)
        {
            return IsValid;
        }

        /// <summary>
        /// Executes the cancel command.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        private void ExecuteCancel(object? parameter)
        {
            try
            {
                LoggingService.LogInfo("Activity update dialog cancelled", "ActivityStatusUpdateDialogViewModel");
                CancelRequested?.Invoke();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error executing cancel command: {ex.Message}", "ActivityStatusUpdateDialogViewModel");
            }
        }

        /// <summary>
        /// Determines whether the cancel command can be executed.
        /// </summary>
        /// <param name="parameter">Command parameter</param>
        /// <returns>Always true</returns>
        private bool CanExecuteCancel(object? parameter)
        {
            return true;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the default values to use when canceling with empty fields.
        /// </summary>
        /// <returns>Tuple of default date and note</returns>
        public (string DefaultDate, string DefaultNote) GetDefaultValues()
        {
            return ("xx/xx/xxxx", "لا توجد معلومات إضافية");
        }

        /// <summary>
        /// Checks if the current values are empty and should use defaults.
        /// </summary>
        /// <returns>True if values are empty</returns>
        public bool ShouldUseDefaults()
        {
            return string.IsNullOrWhiteSpace(UpdateDate) && string.IsNullOrWhiteSpace(UpdateNote);
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes resources and cleans up event subscriptions and references.
        /// Implements enhanced IDisposable pattern with BaseViewModel integration.
        /// Prevents memory leaks from SaveRequested and CancelRequested event handlers.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    LoggingService.LogDebug("Starting disposal of ActivityStatusUpdateDialogViewModel resources", GetType().Name);

                    // Clear dialog event handlers to prevent memory leaks
                    try
                    {
                        SaveRequested = null;
                        CancelRequested = null;
                        LoggingService.LogDebug("Dialog event handlers cleared successfully", GetType().Name);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Error clearing dialog event handlers: {ex.Message}", GetType().Name);
                    }

                    // Clear property references to aid garbage collection
                    _updateDate = null;
                    _updateNote = null;
                    _activityStatus = null;
                    _activityType = null;

                    LoggingService.LogDebug("ActivityStatusUpdateDialogViewModel disposal completed successfully", GetType().Name);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error during ActivityStatusUpdateDialogViewModel disposal: {ex.Message}", GetType().Name);
            }
            finally
            {
                // Always call base disposal to maintain inheritance chain
                base.Dispose(disposing);
            }
        }

        #endregion
    }
}
