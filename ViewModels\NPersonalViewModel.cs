using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows.Threading;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for the NPersonalView providing MVVM data binding and validation.
    /// Implements IDataErrorInfo for real-time phone number validation with Arabic error messages.
    /// Maintains synchronization with PhoneNumbersCollectionModel for dialog integration.
    /// </summary>
    public class NPersonalViewModel : BaseViewModel, IDataErrorInfo
    {
        #region Private Fields

        private string _phoneNumber = string.Empty;
        private string _nameFr = string.Empty;
        private string _nameAr = string.Empty;
        private string _birthDate = string.Empty;
        private string _birthPlace = string.Empty;
        private string _address = string.Empty;
        private string _nationalId = string.Empty;
        private PhoneNumbersCollectionModel? _phoneNumbersCollection;
        private int _gender = 0; // Default to Male (0) - ذكر

        private Dictionary<string, string> _validationErrors = new Dictionary<string, string>();
        private bool _isSynchronizing = false;
        private readonly PersonalInfoValidationService _validationService = new PersonalInfoValidationService();
        private readonly DispatcherTimer _syncTimer;
        private const int SyncDebounceMs = 300;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the NPersonalViewModel class.
        /// Sets up debounced synchronization timer for performance optimization.
        /// </summary>
        public NPersonalViewModel()
        {
            // Initialize debounced synchronization timer
            _syncTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(SyncDebounceMs)
            };
            _syncTimer.Tick += SyncTimer_Tick;
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the phone number.
        /// This property is bound to the PhoneNumberTextBox and includes validation.
        /// Uses debounced synchronization for improved performance.
        /// </summary>
        public string PhoneNumber
        {
            get => _phoneNumber;
            set
            {
                if (SetProperty(ref _phoneNumber, value))
                {
                    // Use debounced sync for better performance
                    DebouncedSyncToCollection();
                }
            }
        }

        /// <summary>
        /// Gets or sets the phone numbers collection for synchronization with dialogs.
        /// </summary>
        public PhoneNumbersCollectionModel? PhoneNumbersCollection
        {
            get => _phoneNumbersCollection;
            set
            {
                if (_phoneNumbersCollection != null)
                {
                    _phoneNumbersCollection.PropertyChanged -= PhoneNumbersCollection_PropertyChanged;
                }

                SetProperty(ref _phoneNumbersCollection, value);

                if (_phoneNumbersCollection != null)
                {
                    _phoneNumbersCollection.PropertyChanged += PhoneNumbersCollection_PropertyChanged;
                    // Sync from collection when it's set
                    SyncFromCollection();
                }
            }
        }

        /// <summary>
        /// Gets or sets the French name (Latin characters).
        /// This property is bound to the NameFrTextBox and includes validation.
        /// Required field for client creation.
        /// </summary>
        public string NameFr
        {
            get => _nameFr;
            set => SetProperty(ref _nameFr, value);
        }

        /// <summary>
        /// Gets or sets the gender selection for profile image display.
        /// 0 = Male (ذكر), 1 = Female (أنثى)
        /// </summary>
        public int Gender
        {
            get => _gender;
            set => SetProperty(ref _gender, value);
        }

        /// <summary>
        /// Gets or sets the Arabic name.
        /// Optional field for client creation.
        /// </summary>
        public string NameAr
        {
            get => _nameAr;
            set => SetProperty(ref _nameAr, value);
        }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format.
        /// Supports partial dates like "xx/xx/1993" and complete dates like "23/03/1993".
        /// Optional field for client creation.
        /// </summary>
        public string BirthDate
        {
            get => _birthDate;
            set => SetProperty(ref _birthDate, value);
        }

        /// <summary>
        /// Gets or sets the birth place.
        /// Optional field for client creation.
        /// </summary>
        public string BirthPlace
        {
            get => _birthPlace;
            set => SetProperty(ref _birthPlace, value);
        }

        /// <summary>
        /// Gets or sets the address.
        /// Optional field for client creation.
        /// </summary>
        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        /// <summary>
        /// Gets or sets the national ID number.
        /// Optional field for client creation.
        /// </summary>
        public string NationalId
        {
            get => _nationalId;
            set => SetProperty(ref _nationalId, value);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles property changes from the phone numbers collection.
        /// </summary>
        private void PhoneNumbersCollection_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PhoneNumbersCollectionModel.PrimaryPhoneNumber))
            {
                SyncFromCollection();
            }
        }

        #endregion

        #region Synchronization Methods

        /// <summary>
        /// Synchronizes the phone number to the collection.
        /// Validation removed as per database binding requirements.
        /// </summary>
        private void SyncToCollection()
        {
            if (_isSynchronizing || PhoneNumbersCollection == null)
                return;

            try
            {
                _isSynchronizing = true;

                var phoneNumber = PhoneNumber?.Trim() ?? string.Empty;

                // Sync phone number to collection without validation
                PhoneNumbersCollection.SetPrimaryPhoneNumber(phoneNumber);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error syncing phone number to collection: {ex.Message}", "NPersonalViewModel");
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        /// <summary>
        /// Synchronizes the phone number from the collection.
        /// </summary>
        private void SyncFromCollection()
        {
            if (_isSynchronizing || PhoneNumbersCollection == null)
                return;

            try
            {
                _isSynchronizing = true;
                var primaryPhone = PhoneNumbersCollection.PrimaryPhoneNumber ?? string.Empty;
                if (PhoneNumber != primaryPhone)
                {
                    _phoneNumber = primaryPhone;
                    OnPropertyChanged(nameof(PhoneNumber));
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error syncing phone number from collection: {ex.Message}", "NPersonalViewModel");
            }
            finally
            {
                _isSynchronizing = false;
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// Gets the validation error message for the specified property.
        /// Only validates NameFr field as per requirements - all other validation logic removed.
        /// </summary>
        /// <param name="propertyName">The name of the property to validate</param>
        /// <returns>Error message if validation fails, null if valid</returns>
        private string? GetValidationError(string propertyName)
        {
            try
            {
                switch (propertyName)
                {
                    case nameof(NameFr):
                        return _validationService.ValidateProperty(propertyName, NameFr);
                    default:
                        return null; // No validation for other fields as per requirements
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in GetValidationError for {propertyName}: {ex.Message}", "NPersonalViewModel");
                return null;
            }
        }

        /// <summary>
        /// Initiates debounced synchronization to collection.
        /// </summary>
        private void DebouncedSyncToCollection()
        {
            try
            {
                _syncTimer.Stop();
                _syncTimer.Start();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initiating debounced sync: {ex.Message}", "NPersonalViewModel");
                SyncToCollection();
            }
        }

        /// <summary>
        /// Handles the sync timer tick event to perform actual synchronization.
        /// </summary>
        private void SyncTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                _syncTimer.Stop();
                SyncToCollection();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in sync timer tick: {ex.Message}", "NPersonalViewModel");
            }
        }

        #endregion

        #region IDataErrorInfo Implementation

        /// <summary>
        /// Gets an error message indicating what is wrong with this object.
        /// Returns null if there are no validation errors.
        /// </summary>
        public string? Error
        {
            get
            {
                // Return the first validation error if any exist
                return _validationErrors.Values.FirstOrDefault();
            }
        }

        /// <summary>
        /// Gets the error message for the property with the given name.
        /// </summary>
        /// <param name="columnName">The name of the property whose error message to get</param>
        /// <returns>The error message for the property, or null if valid</returns>
        public string? this[string columnName]
        {
            get
            {
                try
                {
                    // Clear previous error for this property
                    _validationErrors.Remove(columnName);

                    // Validate the specific property
                    string? errorMessage = GetValidationError(columnName);

                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        _validationErrors[columnName] = errorMessage;
                        return errorMessage;
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error validating property {columnName}: {ex.Message}", "NPersonalViewModel");
                    return null;
                }
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Forces synchronization from the collection to the ViewModel.
        /// Used when the collection is updated externally (e.g., from dialogs).
        /// Bypasses debouncing for immediate synchronization.
        /// </summary>
        public void RefreshFromCollection()
        {
            // Stop any pending debounced sync and perform immediate sync from collection
            _syncTimer.Stop();
            SyncFromCollection();
        }

        /// <summary>
        /// Forces synchronization from the ViewModel to the collection.
        /// Used when the ViewModel is updated externally.
        /// Bypasses debouncing for immediate synchronization.
        /// </summary>
        public void RefreshToCollection()
        {
            // Stop any pending debounced sync and perform immediate sync
            _syncTimer.Stop();
            SyncToCollection();
        }

        /// <summary>
        /// Validates personal information - only NameFr field as per requirements.
        /// All other validation logic removed for database binding implementation.
        /// </summary>
        /// <returns>ValidationResult containing validation errors for NameFr only</returns>
        public ValidationResult ValidateAll()
        {
            try
            {
                var result = new ValidationResult();

                // Only validate NameFr field as per requirements
                var nameFrError = _validationService.ValidateProperty(nameof(NameFr), NameFr);
                if (!string.IsNullOrEmpty(nameFrError))
                {
                    result.AddError(nameof(NameFr), nameFrError);
                }

                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating personal info: {ex.Message}", "NPersonalViewModel");
                var result = new ValidationResult();
                result.AddError("Validation", "حدث خطأ أثناء التحقق من البيانات");
                return result;
            }
        }

        /// <summary>
        /// Forces immediate bidirectional synchronization for dialog operations.
        /// </summary>
        public void ForceImmediateSync()
        {
            try
            {
                _syncTimer.Stop();
                SyncToCollection();
                SyncFromCollection();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error in immediate sync: {ex.Message}", "NPersonalViewModel");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes resources including the sync timer.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    if (_syncTimer != null)
                    {
                        _syncTimer.Stop();
                        _syncTimer.Tick -= SyncTimer_Tick;
                    }

                    // Unsubscribe from collection events
                    if (_phoneNumbersCollection != null)
                    {
                        _phoneNumbersCollection.PropertyChanged -= PhoneNumbersCollection_PropertyChanged;
                    }
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing NPersonalViewModel: {ex.Message}", "NPersonalViewModel");
                }
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
