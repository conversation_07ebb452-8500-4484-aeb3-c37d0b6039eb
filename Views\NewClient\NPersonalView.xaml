<UserControl
    x:Class="UFU2.Views.NewClient.NPersonalView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:UFU2.Common.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:userControls="clr-namespace:UFU2.Views.UserControls"
    d:DesignWidth="800"
    AutomationProperties.HelpText="Personal information form section"
    AutomationProperties.ItemType="Form"
    AutomationProperties.Name="Personal Information Form"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    SnapsToDevicePixels="True"
    Unloaded="NPersonalView_Unloaded"
    UseLayoutRounding="True"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--  PhoneNumberConverter for UFU2 phone number formatting  -->
            <converters:PhoneNumberConverter x:Key="PhoneNumberConverter" />
        </ResourceDictionary>
    </UserControl.Resources>



    <!--  Optimized Grid with performance enhancements  -->
    <Grid UseLayoutRounding="True">
        <Grid.RowDefinitions>
            <RowDefinition Height="27" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header with optimized text rendering  -->
        <TextBlock
            Grid.Row="0"
            HorizontalAlignment="Left"
            Style="{StaticResource SubtitleStyle}"
            Text="المعلومات الشخصية"
            UseLayoutRounding="True" />

        <Grid Grid.Row="1" Margin="12,3">
            <Grid.ColumnDefinitions>
                <!--  Profile Picture Card on Right (will appear on right in RTL)  -->
                <ColumnDefinition Width="127" />
                <!--  Main Information Card on Left (will appear on left in RTL)  -->
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--  Profile Picture - Right Side  -->
            <materialDesign:Card
                Grid.Column="0"
                Height="145"
                Padding="0"
                Style="{StaticResource ContentCardStyle}">

                <!--  ClientProfileImage UserControl with gender-based default images  -->
                <userControls:ClientProfileImage
                    x:Name="ClientProfileImage"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Gender="{Binding Gender, Mode=OneWay, UpdateSourceTrigger=PropertyChanged}"
                    ImageEditRequested="ClientProfileImage_ImageEditRequested" />

            </materialDesign:Card>
            <materialDesign:Card
                Grid.Column="1"
                Height="145"
                Margin="5,0,0,0"
                Style="{StaticResource ContentCardStyle}">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>


                        <!--  الاسم و اللقب(باللاتينية)  -->
                        <TextBox
                            x:Name="NameFrTextBox"
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="الاسم و اللقب(باللاتينية) *"
                            materialDesign:HintAssist.IsFloating="True"
                            LostFocus="NameFrTextBox_LostFocus"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding NameFr, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True, NotifyOnValidationError=True}" />

                        <!--  الاسم واللقب (بالعربية)  -->
                        <TextBox
                            x:Name="NameArTextBox"
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="الاسم واللقب (بالعربية)"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding NameAr, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  تاريخ الازدياد  -->
                        <TextBox
                            x:Name="BirthDateTextBox"
                            Grid.Column="2"
                            MinWidth="81"
                            materialDesign:HintAssist.Hint="تاريخ الازدياد"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding BirthDate, UpdateSourceTrigger=PropertyChanged}" />
                        <!--  مكان ازدياد  -->
                        <TextBox
                            x:Name="BirthPlaceTextBox"
                            Grid.Column="3"
                            MinWidth="81"
                            materialDesign:HintAssist.Hint="مكان ازدياد"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding BirthPlace, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  الجنس  -->
                        <ComboBox
                            x:Name="GenderComboBox"
                            Grid.Column="4"
                            MinWidth="54"
                            materialDesign:HintAssist.Hint="الجنس"
                            SelectedIndex="{Binding Gender, UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource UnderlineComboBoxStyle}">
                            <ComboBoxItem Content="ذكر" />
                            <ComboBoxItem Content="أنثى" />
                        </ComboBox>
                    </Grid>
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="3.5*" />
                            <ColumnDefinition Width="1.8*" />
                            <ColumnDefinition Width="127" />
                        </Grid.ColumnDefinitions>
                        <!--  العنوان  -->
                        <TextBox
                            x:Name="AddressTextBox"
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="العنـــــــوان"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  رقم بطاقة التعريف الوطنية  -->
                        <TextBox
                            x:Name="NationalIdTextBox"
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="رقم بطاقة التعريف الوطنية"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding NationalId, UpdateSourceTrigger=PropertyChanged}" />

                        <!--  رقم الهاتف  -->
                        <TextBox
                            x:Name="PhoneNumberTextBox"
                            Grid.Column="2"
                            materialDesign:HintAssist.Hint="رقم الهاتف"
                            materialDesign:TextFieldAssist.CharacterCounterVisibility="Hidden"
                            LostFocus="PhoneNumberTextBox_LostFocus"
                            MaxLength="13"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                            ToolTip="رقم الهاتف الأساسي" />

                        <Button
                            x:Name="AddPhoneNumbersButton"
                            Grid.Column="2"
                            Margin="9,18"
                            Click="AddPhoneNumbersButton_Click"
                            Style="{StaticResource PlusButtonStyle}"
                            ToolTip="إضافة أرقام الهاتف">
                            <materialDesign:PackIcon
                                Width="18"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Kind="Plus" />
                        </Button>

                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
